<?php

namespace App\Exports;

use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderType;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class CurrencyOrdersExport implements FromQuery, WithColumnFormatting, WithHeadings, WithMapping, WithChunkReading
{
    protected $filters;

    protected $sort;

    protected $direction;

    protected $fulfilledAmountsCache = [];

    public function __construct($filters, $sort, $direction)
    {
        $this->filters = $filters;
        $this->sort = $sort;
        $this->direction = $direction;
    }

    public function query()
    {
        $commissionTypeId = CurrencyOrderType::where('value', 'com')->first()->id;

        return CurrencyOrder::query()
            ->with(['customer', 'inCurrency', 'outCurrency', 'currencyOrderStatus', 'currencyOrderType', 'createdBy'])
            ->where('currency_order_type_id', '!=', $commissionTypeId)
            ->orderBy($this->sort, $this->direction)
            ->filter($this->filters);
    }

    public function chunkSize(): int
    {
        return 1000; // Process 1000 records at a time
    }

    public function headings(): array
    {
        return [
            'Created At',
            'ID',
            'Reference',
            'Currency Order Type',
            'Currency In',
            'Currency Out',
            'Receivable Amount',
            'Fulfilled Receivable Amount',
            'Receivable Balance',
            'Exchange Rate',
            'Payable Amount',
            'Fulfilled Payable Amount',
            'Payable Balance',
            'Processing Fee',
            'Profit And Loss',
            'Code',
            'Customer',
            'Marketing Remarks',
            'Operation Remarks',
            'Status',
            'Cancellation Date',
            'Created By',
        ];
    }

    public function map($order): array
    {
        // Use cached fulfilled amounts or calculate efficiently
        $fulfilledReceivable = $this->getFulfilledReceivable($order->id);
        $fulfilledPayable = $this->getFulfilledPayable($order->id);

        return [
            $order->created_at,
            $order->id,
            $order->reference,
            $order->currencyOrderType->name,
            $order->inCurrency ? $order->inCurrency->code : '',
            $order->outCurrency ? $order->outCurrency->code : '',
            $order->receivable_amount,
            $fulfilledReceivable,
            $order->receivable_amount - $fulfilledReceivable,
            $order->exchange_rate,
            $order->payable_amount,
            $fulfilledPayable,
            $order->payable_amount - $fulfilledPayable,
            $order->processing_fee,
            $order->profit_loss,
            $order->customer ? $order->customer->code : '',
            $order->customer ? $order->customer->name : '',
            $order->marketing_remarks,
            $order->operation_remarks,
            $order->currencyOrderStatus->name,
            $order->currencyOrderStatus->value == 'cancelled' && !empty($order->status_timestamps['cancelled_at']) ?
                \Carbon\Carbon::parse($order->status_timestamps['cancelled_at'])
                    ->setTimezone(config('app.timezone'))
                    ->format('j M Y g:i A') : '',
            $order->createdBy ? $order->createdBy->name : '',
        ];
    }

    public function columnFormats(): array
    {
        $format8Decimals = '#,##0.00000000';

        return [
            'G' => $format8Decimals, // Receivable Amount
            'H' => $format8Decimals, // Fulfilled Receivable Amount
            'I' => $format8Decimals, // Receivable Balance
            'J' => $format8Decimals, // Exchange Rate
            'K' => $format8Decimals, // Payable Amount
            'L' => $format8Decimals, // Fulfilled Payable Amount
            'M' => $format8Decimals, // Payable Balance
            'N' => $format8Decimals, // Processing Fee
            'O' => $format8Decimals, // Profit And Loss
        ];
    }

    protected function getFulfilledReceivable($currencyOrderId)
    {
        // Use cache to avoid repeated queries
        if (!isset($this->fulfilledAmountsCache[$currencyOrderId]['receivable'])) {
            $this->fulfilledAmountsCache[$currencyOrderId]['receivable'] = DB::table('transactions')
                ->join('transaction_statuses', 'transactions.transaction_status_id', '=', 'transaction_statuses.id')
                ->where('currency_order_id', $currencyOrderId)
                ->where('transaction_statuses.value', '!=', 'cancelled')
                ->sum('debit');
        }

        return $this->fulfilledAmountsCache[$currencyOrderId]['receivable'];
    }

    protected function getFulfilledPayable($currencyOrderId)
    {
        // Use cache to avoid repeated queries
        if (!isset($this->fulfilledAmountsCache[$currencyOrderId]['payable'])) {
            $this->fulfilledAmountsCache[$currencyOrderId]['payable'] = DB::table('transactions')
                ->join('transaction_statuses', 'transactions.transaction_status_id', '=', 'transaction_statuses.id')
                ->join('transaction_types', 'transactions.transaction_type_id', '=', 'transaction_types.id')
                ->where('currency_order_id', $currencyOrderId)
                ->where('transaction_statuses.value', '!=', 'cancelled')
                ->where('transaction_types.value', '!=', 'bank_charges')
                ->sum('credit');
        }

        return $this->fulfilledAmountsCache[$currencyOrderId]['payable'];
    }
}
